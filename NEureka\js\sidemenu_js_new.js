var intervalll;
$(document).ready(function(){
    loadTips();
    intervalll = setInterval(startTicker_tips, 3500);
    $("#slider_tips").hover(function(){
     clearInterval(intervalll);
                                  }, function(){
     intervalll = setInterval(startTicker_tips, 3500);
		                                }); 
});

function startTicker_tips(){
 $("#slider_tips li:first").slideUp(function(){
 $(this).appendTo($("#slider_tips")).slideDown();
  });
}
function loadTips() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      displayTips(this);
     }
  };
  xhttp.open("GET", "../xml/useful_tips.xml?pseudoParam="+new Date().getTime(), true);
  xhttp.send();
}
function displayTips(xml) {
  var i,event="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("tip");
  $('#slider_tips').html("");
  for (i = 0; i <x.length; i++) { 
     
     event="<li style='margin-left: 0;'>"
                    +"<span style='display: table-row'>"
					+"<span style='display: table-cell'>&#9755;&nbsp;</span>"
                        // +"<span class='initial_star'>"
                            // +"<img class='flash2' src='data:image/webp;base64,iVBORw0KGgoAAAANSUhEUgAAAaIAAAExAQMAAAAENzTBAAAAA1BMVEX///+nxBvIAAAAAXRSTlMAQObYZgAAACZJREFUeNrtwQENAAAAwqD3T20PBxQAAAAAAAAAAAAAAAAAAADwaUBWAAENb0KQAAAAAElFTkSuQmCC'>"
                        // +"</span>"
                        +"<span style='display: table-cell;text-align: justify;'>"+x[i].childNodes[0].nodeValue+"</span>"
                    +"</span>"
          +"</li>";
     $('#slider_tips').append(event);
  }
}