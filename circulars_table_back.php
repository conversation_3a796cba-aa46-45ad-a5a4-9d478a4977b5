<?php
// Database connection
$host = "localhost";
$user = "root";
$pass = "";
$db   = "frontier_basins_db";

$conn = new mysqli($host, $user, $pass, $db);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$sql = "SELECT * FROM circulars ORDER BY date DESC";
$result = $conn->query($sql);
?>


  <h2>Orders & Circulars</h2>
  <table class="circulars-table" id="circulars-table">
    <thead>
      <tr>
        <th>Description</th>
        <th>Date</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <?php 
      $sl = 1;
      if ($result->num_rows > 0) {
          while($row = $result->fetch_assoc()) {
              echo "<tr>
                     
                      
                      <td>".$row['title']."</td>
					  <td>".date("d/m/Y", strtotime($row['date']))."</td>
                      <td>
                        <a href='circulars/".$row['filename']."' target='_blank' class='view-btn'>View</a>
                        <a href='circulars/".$row['filename']."' download class='download-btn'>Download</a>
                      </td>
                    </tr>";
          }
      } else {
          echo "<tr><td colspan='4'>No circulars uploaded yet.</td></tr>";
      }
      ?>
    </tbody>
  </table>
 <div class="pagination">
    <button id="prevBtn">Previous</button>
    <span id="pageInfo"></span>
    <button id="nextBtn">Next</button>
  </div>
<style>
.circulars-section {
  width: 90%;        /* half width */
  margin: 20px 0 20px 10px; /* top, right, bottom, left -> pushes it a bit right */
  text-align: center;
  clear: both;
}


.circulars-section h2 {
 background: #e6f0ff;    /* light blue background */
  color: #0044cc;         /* dark blue text */
  padding: 12px 20px;
  border: 1px solid #0044cc;
  border-radius: 6px;
  font-size: 20px;
  font-weight: bold;
  display: block;         /* make it a full-width box */
  width: 100%;            /* span the full width of section */
  box-sizing: border-box; /* padding included in width */
  margin-bottom: 15px; 
}

.circulars-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.circulars-table th, .circulars-table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: center;
}

.circulars-table th {
  background: #f4f6f7;
  color: #333;
  font-weight: bold;
}

.view-btn, .download-btn {
  text-decoration: none;
  padding: 6px 12px;
  margin: 2px;
  border-radius: 5px;
  font-weight: bold;
  font-size: 14px;
}

.view-btn {
  background: #007bff;
  color: #fff;
}

.download-btn {
  background: #28a745;
  color: #fff;
}

.view-btn:hover { background: #0056b3; }
.download-btn:hover { background: #1e7e34; }
.pagination {
  margin-top: 10px;
  text-align: center;
}


.pagination {
  margin-top: 10px;
  text-align: right;
}

.pagination button {
  padding: 6px 12px;
  margin: 0 5px;
  background: #0044cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.pagination button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

</style>
<script>
document.addEventListener("DOMContentLoaded", function() {
  const rowsPerPage = 5;
  let currentPage = 1;

  const table = document.getElementById("circulars-table").getElementsByTagName("tbody")[0];
  const rows = table.getElementsByTagName("tr");
  const totalRows = rows.length;
  const totalPages = Math.ceil(totalRows / rowsPerPage);

  const prevBtn = document.getElementById("prevBtn");
  const nextBtn = document.getElementById("nextBtn");
  const pageInfo = document.getElementById("pageInfo");

  function showPage(page) {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    for (let i = 0; i < totalRows; i++) {
      rows[i].style.display = (i >= start && i < end) ? "" : "none";
    }

    pageInfo.textContent = "Page " + page + " of " + totalPages;
    prevBtn.disabled = (page === 1);
    nextBtn.disabled = (page === totalPages);
  }

  prevBtn.addEventListener("click", function() {
    if (currentPage > 1) {
      currentPage--;
      showPage(currentPage);
    }
  });

  nextBtn.addEventListener("click", function() {
    if (currentPage < totalPages) {
      currentPage++;
      showPage(currentPage);
    }
  });

  // Initial load
  showPage(currentPage);
});
</script>

<?php $conn->close(); ?>
