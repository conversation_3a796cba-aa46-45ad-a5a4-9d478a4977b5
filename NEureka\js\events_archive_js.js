$(document).ready(function(){
   loadEvents(); 
});

function loadEvents() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      displayEvents(this);
    }
  };
  xhttp.open("GET", "../../../xml/archives/events_archives.xml", true);
  xhttp.send();
}
function displayEvents(xml) {
  var i,event,is_video,video_tag,vname,on_click_video="",m="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("event");
  
  for (i = 0; i <x.length; i++) { 
    m="Document";
    on_click_video="";    
    is_video=x[i].getElementsByTagName("video")[0].childNodes[0].nodeValue;
  
    if(is_video!=="0"){
      
        vname=x[i].getElementsByTagName("video_link")[0].childNodes[0].nodeValue;
        video_tag="../../frames/ppt_video_new.php?video_name="+vname+"&amp;heading="+is_video;
        on_click_video="basicPopup(this.href);return false;";
        m="<span style='background-color:yellow;color:red;font-size:15px'>&nbsp;Video&nbsp;</span>";
    }
    else 
        video_tag=x[i].getElementsByTagName("link")[0].childNodes[0].nodeValue;
        event="<tr>"
            +"<td class='date_col'>"+(1+i)+"</td>"
            +"<td class='date_col'>"+x[i].getElementsByTagName("date")[0].childNodes[0].nodeValue+"</td>"
            +"<td class='date_col'>"+m+"</td>"
            +"<td>&nbsp;<a href='../"+video_tag+"' onclick='"+on_click_video+"' title='Click to view' target='_BLANK'>"+x[i].getElementsByTagName("title")[0].childNodes[0].nodeValue+"</a>"
            +"</td>"
        +"</tr>";

     $('#events').append(event);
 }
 }
 function basicPopup(url) {
window.open(url,"_BLANK",'height=380,width=650,left=500,top=280,resizable=no,scrollbars=no,toolbar=no,menubar=no,location=no,directories=no, status=yes');
	} 
