<?php include 'header.php'; ?>
<?php
$host = "localhost";
$user = "root";
$pass = "";
$db   = "frontier_basins_db";
$conn = new mysqli($host, $user, $pass, $db);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $title = $_POST['title'];
    $date  = $_POST['date'];
    $targetDir = "circulars/";
    $fileName = time() . "_" . basename($_FILES["file"]["name"]);
    $targetFile = $targetDir . $fileName;

    if (move_uploaded_file($_FILES["file"]["tmp_name"], $targetFile)) {
        $sql = "INSERT INTO circulars (title, date, filename) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $title, $date, $fileName);
        $stmt->execute();
        echo "<p style='color:green;'>Circular uploaded successfully!</p>";
    } else {
        echo "<p style='color:red;'>Error uploading file.</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
  <title>Upload Circular</title>
  <style>
    .upload-form {
      width: 350px;
      margin: 30px auto;
      padding: 20px;
      border: 1px solid #ccc;
      border-radius: 8px;
      background: #f9f9f9;
      font-family: Arial, sans-serif;
    }
    .upload-form h3 {
      text-align: center;
      color: #0044cc;
    }
    .upload-form label {
      display: block;
      margin-top: 10px;
      font-weight: bold;
    }
    .upload-form input {
      width: 100%;
      padding: 7px;
      margin-top: 5px;
    }
    .upload-form button {
      margin-top: 15px;
      width: 100%;
      padding: 10px;
      background: #007bff;
      color: #fff;
      border: none;
      border-radius: 5px;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
    }
    .upload-form button:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <div class="upload-form">
    <h3>Upload Circular</h3>
    <form method="post" enctype="multipart/form-data">
      <label for="title">Title:</label>
      <input type="text" name="title" id="title" required>

      <label for="date">Circular Date:</label>
      <input type="date" name="date" id="date" required>

      <label for="file">Choose File:</label>
      <input type="file" name="file" id="file" required>

      <button type="submit">Upload</button>
    </form>
  </div>
</body>
</html>