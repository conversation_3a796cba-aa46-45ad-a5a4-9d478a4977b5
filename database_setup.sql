-- Select the database first
USE frontier_basins_db;

-- Create circulars table
CREATE TABLE circulars (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  filename VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert some sample data
INSERT INTO circulars (title, date, filename) VALUES
('Annual Budget Circular 2025', '2025-01-15', '1756729873_Annual_Budget_2025.pdf'),
('Safety Guidelines Update', '2025-02-20', '1756729925_Safety_Guidelines.pdf'),
('New Employee Handbook', '2025-03-10', '1756894597_Employee_Handbook.pdf'),
('IT Policy Update', '2025-04-05', '1756894618_IT_Policy.pdf'),
('Holiday Calendar 2025', '2025-05-12', '1756894634_Holiday_Calendar.pdf');
