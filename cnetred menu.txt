<?php
// Database connection
$host = "localhost";
$user = "root";
$pass = "";
$db   = "my_project_db";

$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $title = $_POST['title'];
    $date  = $_POST['date'];

    // File upload
    $targetDir = "circulars/";
    $fileName = basename($_FILES["file"]["name"]);
    $targetFile = $targetDir . $fileName;

    if (move_uploaded_file($_FILES["file"]["tmp_name"], $targetFile)) {
        // Insert into DB
        $sql = "INSERT INTO circulars (title, date, filename) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $title, $date, $fileName);

        if ($stmt->execute()) {
            echo "<p style='color:green;'>Circular uploaded successfully!</p>";
        } else {
            echo "<p style='color:red;'>Error: " . $conn->error . "</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color:red;'>Error uploading file.</p>";
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
  <title>Upload Circular</title>
  <style>
    body {
      font-family: Arial, sans-serif;
    }
    .upload-form {
      width: 400px;
      margin: 40px auto;
      padding: 20px;
      border: 1px solid #ccc;
      border-radius: 8px;
      background: #f9f9f9;
    }
    .upload-form h2 {
      text-align: center;
      color: #0044cc;
    }
    .upload-form label {
      font-weight: bold;
      display: block;
      margin-top: 10px;
    }
    .upload-form input {
      width: 100%;
      padding: 8px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    .upload-form button {
      margin-top: 15px;
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 5px;
      background: #007bff;
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
    }
    .upload-form button:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <div class="upload-form">
    <h2>Upload Circular</h2>
    <form method="post" enctype="multipart/form-data">
      <label for="title">Circular Title:</label>
      <input type="text" name="title" id="title" required>

      <label for="date">Date:</label>
      <input type="date" name="date" id="date" required>

      <label for="file">Choose File:</label>
      <input type="file" name="file" id="file" required>

      <button type="submit">Upload</button>
    </form>
  </div>
</body>
</html>
