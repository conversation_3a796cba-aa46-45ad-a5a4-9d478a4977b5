var interval;
var interval1;
var interval1l;

$(document).ready(function(){

var cmodal = document.getElementById("myModalc");
var emodal = document.getElementById("myModale");
var nmodal = document.getElementById("myModaln");
var hmodal = document.getElementById("myModalh");
//var msgmodal = document.getElementById("myModalMsgs");
var vigimodal = document.getElementById("myModalVigi");

//$(msgmodal).show();

thoughtPopup();
//celebrationPopup();
$(nmodal).fadeIn();

/***UNCOMMENT BELOW ONE TO ADD NEW POP UP MODAL ***/
/*
$('#user_awareness_close').on("click",function(){
   $(nmodal).fadeOut(function(){
     thoughtPopup();
                               });
});*/

/*************************************************/
//var mediaVideo = $("#vigi_msg_video").get(0);
const video = document.getElementById('vigi_msg_video');
$('#vigi_video').on("click",function(){
   $(vigimodal).fadeIn();
   //mediaVideo.load();
   video.currentTime = 0;
   video.autoplay = true;
   video.play();
});
 
$('#vigi_close').on("click",function(){
	//mediaVideo.pause();
	video.pause();
   $(vigimodal).fadeOut();
});

$('#msg_close').on("click",function(){
   //$(msgmodal).fadeOut();
});

$('#hindi_winner').on("click",function(){
   $(hmodal).fadeIn();
});

$('#hindi_winner_close').on("click",function(){
   $(hmodal).fadeOut();
});


$('#view_all_circular').on("click",function(){
   $('#myModalc').fadeIn();
});
$('#view_all_event').on("click",function(){
   $('#myModale').fadeIn();
});

$('#close_event_list').on("click",function(){
   $('#myModale').fadeOut();
});

$('#close_circular_list').on("click",function(){
   $('#myModalc').fadeOut();
});
///added the function to close the special popup, comment this for other pop-ups, updated on 30.12.2022///
/*$('#user_awareness_close').on("click",function(){
   $(nmodal).fadeOut();
});*/


window.onclick = function(event) {

  if (event.target === cmodal) {
    $(cmodal).show();
}
if (event.target === emodal) {
    $(emodal).show();
}

if (event.target === nmodal) {
    $(nmodal).show();
}
}

  $.when(loadEvents()).then(
    $.when(loadCirculars()).then(
	 $.when(loadTodayEvents()).then(
	  //$.when(loadPhotos()).then(

	        /***COMMENT BELOW ONE TO ADD NEW POP UP MODAL***/
             setTimeout(function(){$('.slideshow').show(500,function(){thoughtPopup();});},1500)
			/*************************************************/
			/***UNCOMMENT BELOW ONE TO ADD NEW POP UP MODAL***/
              ///setTimeout(function(){$('.slideshow').show();},1000)
	        /*************************************************/
	  //)
	 )
	)
   );

  var d = new Date();
  var n = d.getFullYear();
  document.getElementById("copyright_year").innerHTML = n;

    $('li.top_row').hover(function(){
         $(this).find('ul.top_row_col_name').show();
     },
     function(){
         $(this).find('ul.top_row_col_name').hide();
               }
);

  document.getElementById("copyright_yearr").innerHTML = n;

    $('li.top_row').hover(function(){
         $(this).find('ul.top_row_col_name').show();
     },
     function(){
         $(this).find('ul.top_row_col_name').hide();
               }
);

/*
interval = setInterval(startTicker, 3500);
    $("#events").hover(function(){
     clearInterval(interval);
                                  }, function(){
     interval = setInterval(startTicker, 3500);
		                                });
*/

/*
     intervall = setInterval(startTicker_cir, 4500);
      $("#circulars").hover(function(){
        clearInterval(intervall);
		                           }, function(){
        intervall = setInterval(startTicker_cir, 4500);
		});
*/
        intervalll = setInterval(startTicker_photos, 3000);

         $("#slider_photos").hover(function(){
	clearInterval(intervalll);
		}, function(){
	intervalll = setInterval(startTicker_photos, 3000);
		});
});

/*function startTicker() {
  $("#events li:first").slideUp("slow","linear",function(){
	$(this).appendTo($("#events")).slideDown("slow","linear");
  });
}
function startTicker_cir() {
			$("#circulars li:first").slideUp("slow","linear",function(){
				$(this).appendTo($("#circulars")).slideDown("slow","linear");
			});
}*/

function startTicker_photos() {
			$("#slider_photos li:first").slideUp(3000,"linear",function(){
				$(this).appendTo($("#slider_photos")).slideDown(3000,"linear");
			});
}

function loadPhotos() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      displayPhotos(this);
    }
  };
  xhttp.open("GET", "../xml/recent_photos_main.xml?pseudoParam="+new Date().getTime(), true);
  xhttp.send();
}
function displayPhotos(xml) {
  var i,event="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("title");
  $("<div class='polaroid' style='position: relative;'>"
           +"<div class='container' style='background-color: #17539e;text-shadow: 1px white;height:40px;border-radius:5px;'>"
             +"<span style='color:white;font-family: \"Calibri\";font-size:21px;'>"+x[0].childNodes[0].nodeValue+"</span>"
           +"</div>"
        +"</div>").insertBefore('div#loader_photos');

x=xmlDoc.getElementsByTagName("photo");
  for (i = 0; i <x.length; i++) {
     event="<li class='r_photo'>"
          +"<img alt='IMG_"+(i+1)+"' src='"+x[i].getElementsByTagName("link")[0].childNodes[0].nodeValue+"' style='width:100%;height:100%;'>"
        +"</li>";
     $(event).insertBefore('div#loader_photos');
  }
 $('div#loader_photos').remove();
}

function loadTodayEvents() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      displayTodayEvents(this);
    }
  };
  xhttp.open("GET", "../xml/today_xml.xml?pseudoParam="+new Date().getTime(), true);
  xhttp.send();
}

function displayTodayEvents(xml) {
  var anchor_tag="",i,event="",is_attr,link="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("today_event");
  var speed = xmlDoc.getElementsByTagName("speed");

  $('div.ticker-move').css("animation-duration",speed[0].childNodes[0].nodeValue);
  $('div.ticker-move').html("|&nbsp;");

  //$('div.ticker-move').append("&nbsp;<div class='ticker-item'><img src='../images/cycle_img.png' style='width:35px;vertical-align:-5px;'/>&nbsp;<a target='_BLANK' href='../circulars/Saksham 2020.pdf' style='color:green;'>Registration for Saksham Cycle Day 2020</a>&nbsp;<img src='../images/cycle_img.png' style='width:35px;vertical-align:-5px;'/></div>&nbsp;|");
  //$('div.ticker-move').append("<div class='ticker-item'><img src='../images/party-pp.png' style='width:25px;vertical-align:-5px;'/>&nbsp;<a href='#' style='color:green;'>Wishing you all a very Happy New Year 2022..!!</a></div>&nbsp;|");
  //$('div.ticker-move').append("|&nbsp;<div class='ticker-item'><img src='../images/party-pp.png' style='width:25px;vertical-align:-5px;'/>&nbsp;<a href='#' style='color:green;'>Hearty Congratulations to all the promotees of WON Basin.. !!</a></div>&nbsp;|&nbsp;");
  //$('div.ticker-move').append("<div class='ticker-item'><img src='../images/66-day.png' style='width:25px;vertical-align:-5px;'/>&nbsp;<a onclick='basicPopup(this.href);return false;' target='_BLANK' href='ppt_video_new.php?video_name=videos/ongc-foundation-day-video.mp4&heading=Celebrating 66<sup>th</sup> ONGC Day' style='color:green;'>Celebrating 66<sup>th</sup> ONGC Day</a>&nbsp;<img src='../images/66-day.png' style='width:25px;vertical-align:-5px;'/></div>&nbsp;|&nbsp;");

	//anchor_tag="<a class='todays' target='_BLANK' href='http://vdaeureka.ongc.co.in/NEureka/modules/photo_gallery/photo_gallery_2024.php?displayTransferresPhotos'>Click here for //New Transferees Welcome Photos</a>";
  for (i = 0; i <x.length; i++) {
      is_attr=x[i].hasAttribute("url");
      link=x[i].getElementsByTagName("link")[0].childNodes[0].nodeValue
      if(link.trim()!=="0" && is_attr===false)
    {
        anchor_tag="<a class='todays' target='_BLANK' href='../"+link+"'>"+x[i].getElementsByTagName("title")[0].childNodes[0].nodeValue+"</a>";
    }
    else{
        anchor_tag="<a class='todays' target='_BLANK' href='"+x[i].getAttribute('url')+"'>"+x[i].getElementsByTagName("title")[0].childNodes[0].nodeValue+"</a>";
    }
      event="<div class='ticker-item'>"
           +anchor_tag
           +"</div>&nbsp;|&nbsp;";
     $('div.ticker-move').append(event);
  }
}

function loadEvents() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      displayEvents(this);
    }
  };
  xhttp.open("GET", "../xml/events_xml.xml?pseudoParam="+new Date().getTime(), true);
  xhttp.send();
}

function displayEvents(xml) {
  var i,event,is_new,is_video,new_sticker,video_tag,vname,on_click_video="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("event");
  $('#events').html("");
  for (i = 0; i <x.length; i++) {
    on_click_video="";
    is_new=x[i].getElementsByTagName("new")[0].childNodes[0].nodeValue;
    is_video=x[i].getElementsByTagName("video")[0].childNodes[0].nodeValue;

    is_new==="1"?new_sticker="&nbsp;<img src='data:image/gif;base64,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' style='vertical-align: middle'/>":new_sticker="";

    if(is_video!=="0"){
        vname=x[i].getElementsByTagName("video_link")[0].childNodes[0].nodeValue;
        video_tag="ppt_video_new.php?video_name=videos/"+vname+"&amp;heading="+is_video;
        on_click_video="basicPopup(this.href);return false;";
    }
    else
        video_tag="../events/"+x[i].getElementsByTagName("link")[0].childNodes[0].nodeValue;

    event="<li class='news_circular'>"
            +"<div>"
             +"<a href='"+video_tag+"' onclick='"+on_click_video+"' title='Click to view' target='_BLANK'>"
                +"<span style='display: table-row;'>"
                       +"<span style='display: table-cell'>&#9755;&nbsp;</span>"
                       +"<span style='display: table-cell;text-align: justify'>"
                         +x[i].getElementsByTagName("title")[0].childNodes[0].nodeValue
                       +"</span>"
                       //+"<span style='display: table-cell;padding-left:8px;'>"+new_sticker+"</span>"
               +"</span>"
             +"</a>"
              +"<div class='news_circular_date'>"
                +x[i].getElementsByTagName("date")[0].childNodes[0].nodeValue
              +"</div>"
           +"</div>"
        +"</li>";

     $('#events').append(event);
  }
  $('#cumulative_event_list').html($('#events').html());
}

function loadCirculars() {
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {

      displayCirculars(this);
    }
  };
  xhttp.open("GET", "../xml/circulars_xml.xml?pseudoParam="+new Date().getTime(), true);
  xhttp.send();
}
function displayCirculars(xml) {

var i,event,is_new,is_video,new_sticker,video_tag,vname,on_click_video="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("circular");
  $('#circulars').html("");
  for (i = 0; i <x.length; i++) {
    on_click_video="";
    is_new=x[i].getElementsByTagName("new")[0].childNodes[0].nodeValue;
    is_video=x[i].getElementsByTagName("video")[0].childNodes[0].nodeValue;

    is_new==="1"?new_sticker="&nbsp;<img src='data:image/webp;base64,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' style='vertical-align: middle'/>":new_sticker="";

    if(is_video!=="0"){
        vname=x[i].getElementsByTagName("video_link")[0].childNodes[0].nodeValue;
        video_tag="ppt_video_new.php?video_name=videos/"+vname+"&amp;heading="+is_video;
        on_click_video="basicPopup(this.href);return false;";
    }
    else
        video_tag="../circulars/"+x[i].getElementsByTagName("link")[0].childNodes[0].nodeValue;

    event="<li class='news_circular'>"
            +"<div>"
             +"<a href='"+video_tag+"' onclick='"+on_click_video+"' title='Click to view' target='_BLANK'>"
                +"<span style='display: table-row;'>"
                       +"<span style='display: table-cell'>&#9755;&nbsp;</span>"
                       +"<span style='display: table-cell;text-align: justify'>"
                         +x[i].getElementsByTagName("title")[0].childNodes[0].nodeValue
                       +"</span>"
                       //+"<span style='display: table-cell;padding-left:8px;'>"+new_sticker+"</span>"
               +"</span>"
             +"</a>"
              +"<div class='news_circular_date'>"
                +x[i].getElementsByTagName("date")[0].childNodes[0].nodeValue
              +"</div>"
           +"</div>"
        +"</li>";


     $('#circulars').append(event);
  }

 $('#cumulative_circular_list').html($('#circulars').html());
}

function basicPopup(url) {
window.open(url,"_BLANK",'height=380,width=650,left=500,top=280,resizable=no,scrollbars=no,toolbar=no,menubar=no,location=no,directories=no, status=yes');
	}
function thoughtPopup() {
var win_height=$(window.parent).height();
var win_width=$(window.parent).width();
var l=win_width/(1.75);
var h=win_height/(2.2);
window.open("../modules/thought_popup/thought.php",'thoughtWindow','height=360px,width=330px,left='+l+'px,top='+h+'px,resizable=no,scrollbars=no,toolbar=no,menubar=no,location=no,directories=no, status=yes');
}


function celebrationPopup() {
var win_height=$(window.parent).height();
var win_width=$(window.parent).width();
var l=win_width/(4);
var h=win_height/(3);
window.open("../manage/php/celebration.php",'celebrationWindow','height=450px,width=450px,left='+l+'px,top='+h+'px,resizable=no,scrollbars=no,toolbar=no,menubar=no,location=no,directories=no, status=yes');
}