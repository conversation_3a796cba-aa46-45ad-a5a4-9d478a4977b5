<?php include 'header.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Frontier Basins</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: #fafafa; /* soft background */
    }
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
   overflow-y: auto;  /* always show vertical scrollbar if content is long */
  overflow-x: hidden; /* disable horizontal scroll (optional) */
}

    /* Header styling */


    /* Navbar styling */
    nav {
      background: #f1f3f4;  /* very light grey */
      border-bottom: 1px solid #dcdcdc;
    }

    nav ul {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: flex-start; /* left align */
	  margin-left: 300px; /*  margin from left */
    }

    nav ul li {
      position: relative;
    }

    nav ul li a {
      display: block;
      padding: 16px 23px;  /*  increased vertical padding for taller navbar */
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: background 0.2s;
    }

    nav ul li a:hover {
      background: #e6e6e6; /* soft hover */
    }

    /* Dropdown menu */
    nav ul li ul {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      background: #f8f9fa;
      min-width: 180px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.05);
      padding: 0;
      margin: 0;
      z-index: 1000;
    }

    nav ul li ul li {
      width: 100%;
    }

    nav ul li:hover ul {
      display: block;
    }

    nav ul li ul li a {
      padding: 10px 15px;
      color: #444;
    }

    nav ul li ul li a:hover {
      background: #eaeaea;
    }

    main {
      padding: 20px;
    }
  </style>
</head>
<body>

 

  <nav>
    <ul>
      <li><a href="#">Home</a></li>
      <li><a href="#">About</a></li>
      <li>
        <a href="#">Support Services ▾</a>
        <ul>
		 
          <li><a target="_BLANK" href="https://fbddnlgts.ongc.co.in/travel/">FB Logistics Portal</a></li>
          <li><a href="#">Web Development</a></li>
          <li><a href="#">SEO</a></li>
        </ul>
      </li>
	  <li>
        <a href="#">Directory ▾</a>
        <ul>
          <li><a href="Manpower_list.pdf" target='_BLANK'>FB Directory</a></li>
          
        </ul>
      </li>
      
	  <li>
        <a href="#">Tools ▾</a>
        <ul>
		 
          <li><a target="_BLANK" href="https://pdftools.ongc.co.in">PDF Tools</a></li>
          
        </ul>
      </li>
      <li><a href="#">Contact</a></li>
    </ul>
  </nav>
  <div style="position:absolute; top:130px; left:20px; width:280px; text-align:center;">
  
  <!-- Head Manager -->
   <div style="position:relative; margin-bottom:20px;">
    <img src="BM.jpg" style="width:100%; min-width:280px; height:auto;" alt="Head Manager" >
    <!-- Text inside image -->
    <!-- Text inside image -->
    <div style="
      position:absolute;
      bottom:8px;
      left:0;
      right:0;
      background:rgba(255,255,255,0.8);
      color:blue;
      padding:5px;
      font-weight:bold;
      border-bottom-left-radius:8px;
      border-bottom-right-radius:8px;
      white-space:nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
    ">
      Dr. R.S.Tandon–Basin Manager
    </div>
  </div>
  <!-- Other Managers Carousel -->
  </div>
  <div class="main-layout">
  
  
  <!-- Carousel -->
  <div class="carousel-container">
    <div class="carousel-slide fade">
      <img src="1.jpg" alt="Image 1"   style="width:90%; min-width:200px; height:auto; ">
	   <div class="carousel-caption">Welcome to Our Company</div>
    </div>
    <div class="carousel-slide fade">
      <img src="2.jpg" alt="Image 2"style="width:90%; min-width:200px; height:auto; ">
	   <div class="carousel-caption">Our Manager Leads with Vision</div>
    </div>
  <div class="circulars-section">
     
      <?php include 'circulars_table.php'; ?>
    </div>
  </div>
  
  

  
<!-- Right Sidebar -->
<!-- Right Sidebar -->
<aside class="sidebar">
  <h3>Important Links</h3>
  <ul>
    <li><a href="#">DISHA</a></li>
    <li><a target="_BLANK" href="https://webice.ongc.co.in/irj/portal">ONGC WEBICE</a></li>
    <li><a target="_BLANK" href="https://mail.ongc.co.in/">ONGC WEBMAIL</a></li>
	 <li><a target="_BLANK" href="https://reports.ongc.co.in">ONGC REPORTS</a></li>
    <li><a href="#">ORGANOGRAM</a></li>
    <li><a href="#">PHOTO GALLERY</a></li>
	<li><a href="#">FEEDBACK</a></li>
	<li><a href="#">FEEDBACK</a></li>
	<li><a href="#">FEEDBACK</a></li>
  </ul>
</aside>
 </div>
<style>
  .sidebar {
  width: 300px;
  height:305px;
  background: #f4f6f7;
  border-left: 1px solid #ddd;
  padding: 20px;
  margin-top: 0px;    /* below navbar */
  position: absolute;  /* take it out of flex flow */
  top: 0;              /* relative to .main-layout */
  right: 20px;         /* stick to the right edge */
  overflow-y: auto; 
}
  .sidebar h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #444;
  }

  .sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .sidebar ul li {
    margin: 0;
    border-bottom: 1px solid #ddd; /* separator line */
  }

  .sidebar ul li:last-child {
    border-bottom: none; /* remove line after last item */
  }

  .sidebar ul li a {
    display: block;
    padding: 10px 0;
    text-decoration: none;
    color: #333;
    font-size: 16px;
    transition: color 0.2s;
  }

  .sidebar ul li a:hover {
    color: #0073e6;
  }
  

</style>

<style>
  /* Flexbox layout */
  .main-layout {
  display: flex;
  justify-content: space-between;   /* keeps carousel centered */
  align-items: flex-start;
  margin-top: 0;
  position: relative;
  

}

.carousel-container {
  width: 60%;
  margin: 0px auto;   /* ensures center alignment */
  
  clear: both;   /* ensures table doesn’t float beside */
  overflow: hidden; 
position: relative;
   
}
.content-area {
  flex: 1;
  padding: 20px;
}





.carousel-slide {
  position: relative;
}

.carousel-slide img {
  
 width: 100%;
  border-radius: 10px;
  
}

/* Caption text */
.carousel-caption {
  position: absolute;
  bottom: 20px;           /* moves text upward */
  left: 50%;
  transform: translateX(-50%);
  color: blue;            /* bold blue text */
  font-weight: bold;
  font-size: 20px;
  background: rgba(255,255,255,0.7); /* semi-transparent background */
  padding: 8px 12px;
  border-radius: 5px;
}

  .carousel-slide {
    display: none;
  }

  .carousel-container img {
  width: 100%;
  height: 100%;            /* forces all images to same height */
  object-fit: cover;       /* crops images without stretching */
  
  }

  .fade { animation: fadeEffect 1.5s; }

  @keyframes fadeEffect {
    from {opacity: 0.4;}
    to {opacity: 1;}
  }

  /* Sidebar */
 

@media (max-width: 1200px) {
  .head-manager {
    width: 200px;
    top: 100px;
  }
}
@media (max-width: 992px) {
  .head-manager {
    width: 180px;
    top: 90px;
  }
}


</style>


<script>
  let slideIndex = 0;
  showSlides();

  function showSlides() {
    let slides = document.getElementsByClassName("carousel-slide");
    for (let i = 0; i < slides.length; i++) {
      slides[i].style.display = "none";  
    }
    slideIndex++;
    if (slideIndex > slides.length) {slideIndex = 1}    
    slides[slideIndex-1].style.display = "block";  
    setTimeout(showSlides, 3000); // Change image every 3 seconds
  }
</script>
  

  <!-- Content -->
  <!--<div class="container my-4">
    <h2>Welcome</h2>
    <p>This is the main content area below the header and navigation bar.</p>
  </div>-->

  <!-- Bootstrap JS -->
  <script src="bootstrap.bundle.min.js"></script>
</body>

</html>
