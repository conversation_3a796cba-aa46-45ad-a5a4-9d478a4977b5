<?php include 'header.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Frontier Basins</title>
  <style>
    body {
      margin: 0;
      fo  /* Flexbox layout */
  .main-layout {
  display: flex;
  justify-content: space-between;   /* keeps carousel centered */
  align-items: flex-start;
  margin-top: 0;
  position: relative;
  min-height: 400px;
}

.carousel-container {
  width: 60%;
  margin: 0px auto;   /* ensures center alignment */
  clear: both;   /* ensures table doesn't float beside */
  overflow: hidden; 
  position: relative;sans-serif;
      background: #fafafa; /* soft background */
    }
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
   overflow-y: auto;  /* always show vertical scrollbar if content is long */
  overflow-x: hidden; /* disable horizontal scroll (optional) */
}

    /* Header styling */


    /* Navbar styling */
    nav {
      background: #f1f3f4;  /* very light grey */
      border-bottom: 1px solid #dcdcdc;
      position: relative;
    }

    nav ul {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: flex-start; /* left align */
	  margin-left: 300px; /*  margin from left */
      flex-wrap: wrap; /* Allow wrapping on smaller screens */
    }

    nav ul li {
      position: relative;
    }

    nav ul li a {
      display: block;
      padding: 16px 23px;  /*  increased vertical padding for taller navbar */
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: background 0.2s;
      white-space: nowrap; /* Prevent text wrapping */
    }

    nav ul li a:hover {
      background: #e6e6e6; /* soft hover */
    }

    /* Dropdown menu */
    nav ul li ul {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      background: #f8f9fa;
      min-width: 180px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.05);
      padding: 0;
      margin: 0;
      z-index: 1000;
    }

    nav ul li ul li {
      width: 100%;
    }

    nav ul li:hover ul {
      display: block;
    }

    nav ul li ul li a {
      padding: 10px 15px;
      color: #444;
    }

    nav ul li ul li a:hover {
      background: #eaeaea;
    }

    main {
      padding: 20px;
    }
  </style>
</head>
<body>

 

  <nav>
    <button class="mobile-menu-btn" onclick="toggleMobileMenu()">☰</button>
    <ul id="navMenu">
      <li><a href="#">Home</a></li>
      <li><a href="#">About</a></li>
      <li>
        <a href="#">Support Services ▾</a>
        <ul>
		 
          <li><a target="_BLANK" href="https://fbddnlgts.ongc.co.in/travel/">FB Logistics Portal</a></li>
          <li><a href="#">Web Development</a></li>
          <li><a href="#">SEO</a></li>
        </ul>
      </li>
	  <li>
        <a href="#">Directory ▾</a>
        <ul>
          <li><a href="Manpower_list.pdf" target='_BLANK'>FB Directory</a></li>
          
        </ul>
      </li>
      
	  <li>
        <a href="#">Tools ▾</a>
        <ul>
		 
          <li><a target="_BLANK" href="https://pdftools.ongc.co.in">PDF Tools</a></li>
          
        </ul>
      </li>
      <li><a href="#">Contact</a></li>
    </ul>
  </nav>
  <div class="head-manager">
  
  <!-- Head Manager -->
   <div style="position:relative; margin-bottom:20px;">
    <img src="BM.jpg" style="width:100%; height:auto;" alt="Head Manager" >
    <!-- Text inside image -->
    <!-- Text inside image -->
    <div style="
      position:absolute;
      bottom:8px;
      left:0;
      right:0;
      background:rgba(255,255,255,0.8);
      color:blue;
      padding:5px;
      font-weight:bold;
      border-bottom-left-radius:8px;
      border-bottom-right-radius:8px;
      white-space:nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
      font-size: 14px;
    ">
      Dr. R.S.Tandon–Basin Manager
    </div>
  </div>
  <!-- Other Managers Carousel -->
  </div>
  <div class="main-layout">
  
  
  <!-- Carousel -->
  <div class="carousel-container">
    <div class="carousel-slide fade">
      <img src="1.jpg" alt="Image 1"   style="width:90%; min-width:200px; height:auto; ">
	   <div class="carousel-caption">Welcome to Our Company</div>
    </div>
    <div class="carousel-slide fade">
      <img src="2.jpg" alt="Image 2"style="width:90%; min-width:200px; height:auto; ">
	   <div class="carousel-caption">Our Manager Leads with Vision</div>
    </div>
  <div class="circulars-section">
     
      <?php include 'circulars_table.php'; ?>
    </div>
  </div>
  
  

  
<!-- Right Sidebar -->
<!-- Right Sidebar -->
<aside class="sidebar">
  <h3>Important Links</h3>
  <ul>
    <li><a href="#">DISHA</a></li>
    <li><a target="_BLANK" href="https://webice.ongc.co.in/irj/portal">ONGC WEBICE</a></li>
    <li><a target="_BLANK" href="https://mail.ongc.co.in/">ONGC WEBMAIL</a></li>
	 <li><a target="_BLANK" href="https://reports.ongc.co.in">ONGC REPORTS</a></li>
    <li><a href="#">ORGANOGRAM</a></li>
    <li><a href="#">PHOTO GALLERY</a></li>
	<li><a href="#">FEEDBACK</a></li>
  </ul>
</aside>
 </div>
<style>
  .sidebar {
  width: 300px;
  height:305px;
  background: #f4f6f7;
  border-left: 1px solid #ddd;
  padding: 20px;
  margin-top: 0px;    /* below navbar */
  position: absolute;  /* take it out of flex flow */
  top: 0;              /* relative to .main-layout */
  right: 20px;         /* stick to the right edge */
  overflow-y: auto; 
}
  .sidebar h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #444;
  }

  .sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .sidebar ul li {
    margin: 0;
    border-bottom: 1px solid #ddd; /* separator line */
  }

  .sidebar ul li:last-child {
    border-bottom: none; /* remove line after last item */
  }

  .sidebar ul li a {
    display: block;
    padding: 10px 0;
    text-decoration: none;
    color: #333;
    font-size: 16px;
    transition: color 0.2s;
  }

  .sidebar ul li a:hover {
    color: #0073e6;
  }

/* Head Manager Positioning */
.head-manager {
  position: absolute;
  top: 130px;
  left: 20px;
  width: 280px;
  text-align: center;
}

</style>

<style>
  /* Flexbox layout */
  .main-layout {
  display: flex;
  justify-content: space-between;   /* keeps carousel centered */
  align-items: flex-start;
  margin-top: 0;
  position: relative;
  

}

.carousel-container {
  width: 60%;
  margin: 0px auto;   /* ensures center alignment */
  
  clear: both;   /* ensures table doesn’t float beside */
  overflow: hidden; 
position: relative;
   
}
.content-area {
  flex: 1;
  padding: 20px;
}





.carousel-slide {
  position: relative;
}

.carousel-slide img {
  
 width: 100%;
  border-radius: 10px;
  
}

/* Caption text */
.carousel-caption {
  position: absolute;
  bottom: 10px;            /* Moved down - closer to bottom */
  left: 45%;               /* Moved left from center */
  transform: translateX(-50%); /* Adjust centering for new left position */
  color: blue;             /* bold blue text */
  font-weight: bold;
  font-size: 20px;
  background: rgba(255,255,255,0.8); /* slightly more opaque background */
  padding: 12px 16px;      /* increased padding for better visibility */
  border-radius: 8px;      /* slightly more rounded */
  text-align: center;      /* center text within the box */
  white-space: nowrap;     /* prevent text wrapping */
  box-shadow: 0 2px 8px rgba(0,0,0,0.2); /* subtle shadow for depth */
}

  .carousel-slide {
    display: none;
  }

  .carousel-container img {
  width: 100%;
  height: 100%;            /* forces all images to same height */
  object-fit: cover;       /* crops images without stretching */
  
  }

  .fade { animation: fadeEffect 1.5s; }

  @keyframes fadeEffect {
    from {opacity: 0.4;}
    to {opacity: 1;}
  }

  /* Responsive Media Queries */
  
/* Large Desktop */
@media (max-width: 1400px) {
  nav ul {
    margin-left: 250px;
  }
  .head-manager {
    width: 260px;
  }
  .sidebar {
    width: 280px;
  }
}

/* Desktop */
@media (max-width: 1200px) {
  nav ul {
    margin-left: 200px;
  }
  .head-manager {
    width: 220px;
    top: 120px;
  }
  .sidebar {
    width: 260px;
    right: 10px;
  }
  .carousel-container {
    width: 65%;
  }
}

/* Tablet Landscape */
@media (max-width: 992px) {
  nav ul {
    margin-left: 150px;
    padding: 0 10px;
  }
  nav ul li a {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .head-manager {
    width: 180px;
    top: 110px;
    left: 10px;
  }
  
  .main-layout {
    flex-direction: column;
    align-items: center;
  }
  
  .carousel-container {
    width: 80%;
    margin: 20px auto;
  }
  
  .sidebar {
    position: relative;
    width: 90%;
    max-width: 600px;
    height: auto;
    right: 0;
    top: 20px;
    margin: 20px auto;
  }
  
  .circulars-section {
    width: 95%;
    margin: 20px auto;
  }
}

/* Tablet Portrait */
@media (max-width: 768px) {
  nav ul {
    margin-left: 20px;
    flex-direction: column;
    background: #f1f3f4;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: none;
  }
  
  nav ul.show {
    display: flex;
  }
  
  nav ul li {
    width: 100%;
  }
  
  nav ul li a {
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
  }
  
  /* Mobile Menu Button */
  .mobile-menu-btn {
    display: block;
    background: none;
    border: none;
    font-size: 24px;
    padding: 10px;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
  }
  
  .head-manager {
    position: relative;
    width: 90%;
    max-width: 300px;
    top: 0;
    left: 0;
    margin: 20px auto;
  }
  
  .carousel-container {
    width: 95%;
  }
  
  .carousel-caption {
    font-size: 16px;
    padding: 10px 12px;
    transform: translateX(-50%); /* maintain horizontal centering on tablet */
    bottom: 8px;  /* Moved down on tablet */
    left: 45%;    /* Moved left on tablet */
  }
  
  .sidebar {
    width: 95%;
    padding: 15px;
  }
  
  .sidebar h3 {
    font-size: 18px;
  }
  
  .sidebar ul li a {
    font-size: 14px;
    padding: 8px 0;
  }
}

/* Mobile */
@media (max-width: 576px) {
  nav ul li a {
    padding: 12px 15px;
    font-size: 14px;
  }
  
  .head-manager {
    width: 95%;
    margin: 15px auto;
  }
  
  .head-manager img {
    max-width: 250px;
  }
  
  .carousel-container {
    width: 98%;
    margin: 15px auto;
  }
  
  .carousel-caption {
    font-size: 14px;
    padding: 8px 10px;
    transform: translateX(-50%); /* maintain horizontal centering on mobile */
    bottom: 6px;  /* Moved down on mobile */
    left: 45%;    /* Moved left on mobile */
    white-space: normal; /* allow text wrapping on very small screens */
    max-width: 90%; /* prevent caption from being too wide */
  }
  
  .sidebar {
    width: 98%;
    padding: 10px;
    height: auto;
  }
  
  .sidebar h3 {
    font-size: 16px;
    margin-bottom: 10px;
  }
  
  .sidebar ul li a {
    font-size: 13px;
    padding: 6px 0;
  }
  
  .circulars-section {
    width: 98%;
    margin: 15px auto;
  }
  
  .circulars-section h2 {
    font-size: 18px;
    padding: 10px 15px;
  }
  
  .circulars-table th,
  .circulars-table td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .view-btn, .download-btn {
    padding: 4px 8px;
    font-size: 11px;
    margin: 1px;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .head-manager img {
    max-width: 200px;
  }
  
  .carousel-caption {
    font-size: 12px;
    padding: 6px 8px;
    transform: translateX(-50%); /* maintain horizontal centering on extra small screens */
    bottom: 5px;  /* Moved down on small mobile */
    left: 45%;    /* Moved left on small mobile */
    max-width: 85%; /* smaller max width for tiny screens */
  }
  
  .circulars-table {
    font-size: 11px;
  }
  
  .circulars-table th,
  .circulars-table td {
    padding: 6px 2px;
  }
  
  .view-btn, .download-btn {
    padding: 3px 6px;
    font-size: 10px;
    display: block;
    margin: 2px 0;
    text-align: center;
  }
}

/* Hide mobile menu button by default */
.mobile-menu-btn {
  display: none;
}


</style>


<script>
  let slideIndex = 0;
  showSlides();

  function showSlides() {
    let slides = document.getElementsByClassName("carousel-slide");
    for (let i = 0; i < slides.length; i++) {
      slides[i].style.display = "none";  
    }
    slideIndex++;
    if (slideIndex > slides.length) {slideIndex = 1}    
    slides[slideIndex-1].style.display = "block";  
    setTimeout(showSlides, 3000); // Change image every 3 seconds
  }

  // Mobile menu toggle function
  function toggleMobileMenu() {
    const navMenu = document.getElementById('navMenu');
    navMenu.classList.toggle('show');
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const nav = document.querySelector('nav');
    const navMenu = document.getElementById('navMenu');
    const mobileBtn = document.querySelector('.mobile-menu-btn');
    
    if (!nav.contains(event.target) && navMenu.classList.contains('show')) {
      navMenu.classList.remove('show');
    }
  });

  // Handle window resize
  window.addEventListener('resize', function() {
    const navMenu = document.getElementById('navMenu');
    if (window.innerWidth > 768) {
      navMenu.classList.remove('show');
    }
  });
</script>
  

  <!-- Content -->
  <!--<div class="container my-4">
    <h2>Welcome</h2>
    <p>This is the main content area below the header and navigation bar.</p>
  </div>-->

  <!-- Bootstrap JS -->
  <script src="bootstrap.bundle.min.js"></script>
</body>

</html>
