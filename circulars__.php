CREATE TABLE circulars (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  filename VARCHAR(255) NOT NULL
);
code below carousal

<?php
// Database connection
$host = "localhost";
$user = "root";
$pass = "";
$db   = "frontier_basins_db";

$conn = new mysqli($host, $user, $pass, $db);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$sql = "SELECT * FROM circulars ORDER BY date DESC";
$result = $conn->query($sql);
?>

<div class="circulars-section">
  <h2>Orders & Circulars</h2>
  <table class="circulars-table">
    <thead>
      <tr>
        <th>Sl No</th>
        <th>Date</th>
        <th>Title</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <?php 
      $sl = 1;
      if ($result->num_rows > 0) {
          while($row = $result->fetch_assoc()) {
              echo "<tr>
                      <td>".$sl++."</td>
                      <td>".date("d-M-Y", strtotime($row['date']))."</td>
                      <td>".$row['title']."</td>
                      <td>
                        <a href='circulars/".$row['filename']."' target='_blank' class='view-btn'>View</a>
                        <a href='circulars/".$row['filename']."' download class='download-btn'>Download</a>
                      </td>
                    </tr>";
          }
      } else {
          echo "<tr><td colspan='4'>No circulars uploaded yet.</td></tr>";
      }
      ?>
    </tbody>
  </table>
</div>

<?php $conn->close(); ?>

---------------------------------------------------------
form for uploading circulars upload.php
----------------------------------
<?php
$host = "localhost";
$user = "root";
$pass = "";
$db   = "frontier_basins_db";
$conn = new mysqli($host, $user, $pass, $db);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $title = $_POST['title'];
    $date  = $_POST['date'];
    $targetDir = "circulars/";
    $fileName = time() . "_" . basename($_FILES["file"]["name"]);
    $targetFile = $targetDir . $fileName;

    if (move_uploaded_file($_FILES["file"]["tmp_name"], $targetFile)) {
        $sql = "INSERT INTO circulars (title, date, filename) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $title, $date, $fileName);
        $stmt->execute();
        echo "<p style='color:green;'>Circular uploaded successfully!</p>";
    } else {
        echo "<p style='color:red;'>Error uploading file.</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
  <title>Upload Circular</title>
  <style>
    .upload-form {
      width: 350px;
      margin: 30px auto;
      padding: 20px;
      border: 1px solid #ccc;
      border-radius: 8px;
      background: #f9f9f9;
      font-family: Arial, sans-serif;
    }
    .upload-form h3 {
      text-align: center;
      color: #0044cc;
    }
    .upload-form label {
      display: block;
      margin-top: 10px;
      font-weight: bold;
    }
    .upload-form input {
      width: 100%;
      padding: 7px;
      margin-top: 5px;
    }
    .upload-form button {
      margin-top: 15px;
      width: 100%;
      padding: 10px;
      background: #007bff;
      color: #fff;
      border: none;
      border-radius: 5px;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
    }
    .upload-form button:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <div class="upload-form">
    <h3>Upload Circular</h3>
    <form method="post" enctype="multipart/form-data">
      <label for="title">Title:</label>
      <input type="text" name="title" id="title" required>

      <label for="date">Date:</label>
      <input type="date" name="date" id="date" required>

      <label for="file">Choose File:</label>
      <input type="file" name="file" id="file" required>

      <button type="submit">Upload</button>
    </form>
  </div>
</body>
</html>

-------------------------------------------
CSS for TABLE((add in your main CSS file)
------------------------------------

.circulars-section {
  width: 90%;
  margin: 40px auto;
}

.circulars-section h2 {
  text-align: center;
  margin-bottom: 15px;
  color: #0044cc;
}

.circulars-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.circulars-table th, .circulars-table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: center;
}

.circulars-table th {
  background: #f4f6f7;
  color: #333;
  font-weight: bold;
}

.view-btn, .download-btn {
  text-decoration: none;
  padding: 6px 12px;
  margin: 2px;
  border-radius: 5px;
  font-weight: bold;
  font-size: 14px;
}

.view-btn {
  background: #007bff;
  color: #fff;
}

.download-btn {
  background: #28a745;
  color: #fff;
}

.view-btn:hover { background: #0056b3; }
.download-btn:hover { background: #1e7e34; }
