
$(document).ready(function(){
loadPhotos();
});

function loadPhotos() {  
  var xhttp = new XMLHttpRequest();
  xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      displayPhotos(this);
    }
  };
  xhttp.open("GET", "/NEureka/xml/recent_photos_main.xml?pseudoParam="+new Date().getTime(), true);
  xhttp.send();
}
function displayPhotos(xml) {
  var i,event="";
  var xmlDoc = xml.responseXML;
  var x = xmlDoc.getElementsByTagName("title");
  $("<div class='polaroid' style='position: relative;'>"
           +"<div class='container' style='background-color: #17539e;text-shadow: 1px white;height:40px;border-radius:5px;'>"
             +"<span style='color:white;font-family: \"Calibri\";font-size:21px;'>"+x[0].childNodes[0].nodeValue+"</span>"
           +"</div>"
        +"</div>").insertBefore('div#loader_photos');

x=xmlDoc.getElementsByTagName("photo");
event1="<div class='w3-content w3-display-container' style='height:70%;'>";
event2="";
  for (i = 0; i <x.length; i++) {
	  photo_link=(x[i].getElementsByTagName("link")[0].childNodes[0].nodeValue).replace("..", "/NEureka");;
     event2 +="<div class='w3-display-container mySlides'>"
          +"<img alt='IMG_"+(i+1)+"' src='"+photo_link+"' style='width:100%;height:100%;'></div>";
  }
  event3="<button class='w3-button w3-display-left w3-black' onclick='plusDivs(-1)'>&#10094;</button><button class='w3-button w3-display-right w3-black' onclick='plusDivs(1)'>&#10095;</button></div>";
  $(event1+event2+event3).insertBefore('div#loader_photos');
 $('div#loader_photos').remove(); 
}
