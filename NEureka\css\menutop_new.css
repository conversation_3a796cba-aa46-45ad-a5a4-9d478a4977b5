body::-webkit-scrollbar {
    width: 3px;
    height:5px;
	font-family: <PERSON><PERSON><PERSON>;
} 
*[lang="ar"] {font-family: "Cal<PERSON>ri",serif; font-size: 16px !important;} /** for arabic **/

body::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
} 
body::-webkit-scrollbar-thumb {
  background-color: darkgrey;
  outline: 2px solid slategrey;
}
body,ul,li{ 
	margin:0;
	padding:0;
}
html {
	margin:0;
	padding:0;
}
fieldset,img { 
	border:0;
}
ul,li {
	list-style:none;
}
.container{
	position: relative;
	text-align: center;
}

.slideshow,.slideshow:before {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    z-index: -2000  ;
    opacity:0.55;
}
.slideshow:before{
    content: '';
    /* background: transparent url('data:image/webp;base64,UklGRkQAAABXRUJQVlA4WAoAAAAQAAAAAQAAAQAAQUxQSAUAAAAA4wAAAABWUDggGAAAADABAJ0BKgIAAgABABwlpAADcAD+/gbQAA==') repeat top left; */
}

.slideshow li span {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    opacity: 0.3;
    z-index: 0;  
}

.menubar{
	width: 100%;
	height: 50px;
	margin: 0px auto;
    /* opacity:0.80; */
    font-family: Calibri;
    z-index: 10000;
	position:absolute;
	background-color: #17539e;
	}
.menubar_2{
	width: 100%;
    opacity:1;
    font-family: Calibri;
    z-index: -1500;
    position:absolute;
	width:45%;
	top:72%;
  }
  
  .menubar_3{
    opacity:1;
    font-family: Calibri;
    z-index: -1500;
    position:absolute;
	left: 83%;
	width:15%;
  }
  
   .menubar_4{
    opacity:1;
    font-family: Calibri;
    z-index: -1500;
    position:absolute;
	left: 83%;
	width:15%;
	top:85%;
  }
  
  
.top_row_col li ul
{
  position: absolute;
  top: 0;
  left: 100%;
  display:none;
 }
.top_row_col li:hover > ul
{
display:inline;
}
li.top_row{
  background-color: #17539e;
  font-family: Calibri;
  font-size: 20px;
  float: left;
  color: white;
  text-decoration: none;
  _letter-spacing: 0.03em;
  cursor: default;
  border-right: 1px solid white;
  padding: 5px 0px 5px 0;
  margin:0; 
  text-align: center;
  xwidth:130px;
   }
 }
.top_row_col li{
  color: white;
  text-align: left;
  text-decoration: none;
  letter-spacing: 0.03em;
  background-color: #17539e;    
  position: relative;
  font-family: Calibri;
  font-size: 18px;
    }    
a{
        text-decoration: none;
        color:white;
}

.top_row_col li:hover{
    background-color: maroon;
}

li.top_row:hover{
    background-color: #17539e;
}

li:not(.top_row):not(.news_circular):not(.todays_event):not(.r_photo):hover{
    background-color:maroon; 
}
.top_row_col_name li{
position:relative;
background:#17539e;
font-family:calibri;
font-size: 14px;
}
.top_row_col_name li:hover{
background-color:maroon;
}
.top_row_col_name li ul
{
        position: absolute;
        top: 0;
        left: 100%;
        display:none;
}
.top_row_col_name li:hover > ul
    {
        display:block;
    }
.top_row_col_name_li{
padding:4px 6px 4px 12px;
text-align: left;

}
.top_row_col_name_li_first{
/* margin-top: 4px; */
}
.has_children{  
    font-size:16px;
    float: right;
    display: inline;
}
.initial_bullet{   
    font-size:16px;
    display: table-cell;
	margin-right:4px;
}
.tag1{
font-family: Calibri;
box-shadow: inset 1px 1px 8px 1px white;
letter-spacing: 0.09em;
font-size: 16.5px;
border-left: 1px solid white;
border-radius:50px 50px 50px 50px;
/*background-color: #a70000;*/
background-color: #4286f5;
color:white;
padding:8px 15px 8px 10px;
text-align: center;
        }
.tag1_p{
border-left:0px solid white;
margin-bottom:0px;
margin-top: 0;
margin-left:8px;
        }

li.news_circular{
   padding: 3px;
   letter-spacing: 0.06em;
   color:maroon;
   font-weight: 600;
   font-size: 16px;
   width:95%;
  }
		
div#events_div,div#circular_div{
   height:20vw;
   margin-left:45px;
   overflow: hidden;
   opacity:0.85;
   display:inline-block;
   border: 0px solid maroon;
   border-radius: 15px;
   width:60%;
   z-index: 7000;
   box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
   margin-bottom: 2px;
   margin-right: 5px;
   margin-top:8px;
}

div#guide_div,div#training_div{
   height:15vw;
   margin-left:0px;
   overflow: hidden;
   opacity:0.85;
   display:inline-block;
   border: 0px solid maroon;
   border-radius: 15px;
   width:30%;
   z-index: 7000;
   box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
   margin-bottom: 2px;
   margin-right: 5px;
   margin-top:8px;
}
div.polaroid {  
  background-color: white;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  margin-bottom: 10px;
}

div.container {
  text-align: center;
  padding:0px;
  margin-bottom: -5px;
}

@keyframes ticker {
    0% { transform: translate3d(0, 0, 0); }
    100% { transform: translate3d(-100%, 0, 0); }
  }
  
.tcontainer{
    width: 75.5vw;
    overflow: hidden;
  }
.ticker-wrap {
    width: 100%;
    padding-left:100%;
  }
.ticker-move {
    display: inline-block;
    white-space: nowrap;
    padding-right: 100%;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    animation-name: ticker;
/*    animation-duration:35s;*/
  }
.ticker-move:hover{
    animation-play-state: paused;
  }
.ticker-item{
    display: inline;
    color:purple;
    padding: 0 10px 0 10px;
    font-size: 16.5px;
    font-weight: bold;
    font-family: Calibri;
    letter-spacing: 0.06em;
  }    
        
  a.todays{
      color:maroon;
  }
  a.todays:hover{
      color:green;
  }
  
  li.news_circular a{
      text-decoration: none;
      color:maroon;
	  font-size:15px;
      font-family: Calibri;
	  font-height:15px;
  }
  li.news_circular a:hover{
      padding-left: 8px;
  }
  .top_row_col_name_li a{
      display: table-cell;padding: 0 2px 0 0;
	  font-size:14px;
  }
  
  /* div.news_circular_date{ */
      /* font-size:15px; */
      /* font-family: Calibri; */
      /* color:purple; */
      /* margin-left:4.5%; */
      /* font-weight: normal; */
      /* margin-top: 0 */
  /* } */
  div#footer{
      position:absolute;
      width:100%;
      top:160%;  //initially 120%
      bottom:0;
      margin-bottom: 0;
      z-index:7000;
	  width:100%;
  }
  
    div#footer_test{
      position:absolute;
      width:100%;
      top:160%;  //initially 120%
      bottom:0;
      margin-bottom: 0;
      z-index:7000;
	  width:100%;
  }
  
  div#footer_photo{
      position:absolute;
      width:100%;
      top:130%;  //initially 120%
      bottom:0;
      margin-bottom: 0;
      z-index:7000;
	  width:100%;
  }
  
  .responsive_photo {
  padding-top: 6px;
  padding-right: 6px;
  padding-bottom: 6px;
  padding-left: 6px;
  float: left;
  width: 16%;
  position: relative;
}

.container_image:hover .image {
  opacity: 0.2;
}

.container_image:hover .middle {
  opacity: 1;
}

div.gallery {
  border: 1px solid #ccc;
  background-color: #145b85;
  box-shadow:2px 2px 2px #aaaaaa;
}

div.gallery:hover {
   
  box-shadow: 2px 2px 5px 1px #75aed7;
}

div.gallery img {
  width: 100%;
  height: auto;
}

.middle {
  transition: .5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
}


.text-photo {
    color: #fff!important;
	font-family: 'Century Gothic';
}
  
 div#desktop
{
	position:absolute;
	top:123%;
	font-family:Calibri;
	font-size:16px;width:89%;
	text-align:center;
	background-color:#7fbf1b;
	height:80px;
	width:100%;
	left: 2%;
 }
 
  div#desktop_test
{
	position:absolute;
	top:105%;
	font-family:Calibri;
	font-size:16px;width:89%;
	text-align:center;
	background-color:#7fbf1b;
	height:110px;
	width:100%;
	left: 2%;
 }
 
  @font-face {
  font-family: Calibri;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  _src: local('Pacifico Regular'), local('Pacifico-Regular'), url(../fonts/font-pacifo.woff2) format('woff2');
}
img.feedback_btn{
    cursor: pointer;
    width:50px;
    vertical-align: middle;
    font-family: Calibri;
}
img.feedback_btn:hover{
    width:54px;
}

.modal-all-view {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 20000; /* Sit on top */
  padding-top: 15px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content{
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  height:50%;
  overflow-y:scroll;
  margin-left: 20%;
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
.modal-content::-webkit-scrollbar {
    width: 5px;
}
 
.modal-content::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
}
.modal-content::-webkit-scrollbar-thumb {
  background-color: darkgrey;
  outline: 1px solid slategrey;
}

.divOtherSite{
	top: 400%;
	position: absolute;
	transform: translate(-50%, -50%);
	margin: auto !important;
	left:52%;
	width:100%;
	}
	
.otherSite{
	text-align:center;
	border: 0px solid;
	border-radius: 5px;
	background-color:#1f4c65;
	width:100px;
	height:50px;
	border-radius: 0 0 5px 5px;
}
	
.newsflasht{
	background-color:#347d34;
	width:140px;
	height:50px;
	text-align:center;
	line-height: 50px;
}

.shabda{
	background-color:#6a1755;
	width:180px;
	height:50px;
	text-align:center;
	line-height: 50px;
	color: white;
    font-family: Calibri;
	font-size: 20px;
}

.blinkerText
{
	animation: blinker 1.5s linear infinite;
}


.text-white {
        color: white;
        font-family: Calibri;
		font-size: 20px;
       }
	   
	   
@keyframes blinker {
            50% {
                opacity: 0;
            }
        }
.news_circular_date {
    font-size: 13px;
    padding: 4px 8px;
    background-color: #c82333 !important;
    color: #fff !important;
    font-weight: 600;
    border-radius: 20px;
	width:20%;
}

div#tips_div{
            height:7cm;
            overflow: hidden;
            margin-bottom:5%;
			margin-top:1%;
            box-shadow:4px 4px 2px #aaaaaa;
            margin-left:5%;
            width:99%;
            border-radius: 0 0 5px 5px;
            background-color: #ffe699
        }
		
		div#tips_div_test{
            height:5cm;
            overflow: hidden;
            margin-bottom:5%;
			margin-top:1%;
            box-shadow:4px 4px 2px #aaaaaa;
            margin-left:5%;
            width:99%;
            border-radius: 0 0 5px 5px;
            background-color: #ffe699
        }
		
div#tips_div li{
    padding:5px;
    font-family: Calibri;
    font-size: 16px;
}
div#tips_div li:hover{
    color:green;
    font-weight: 900;
	background-color: #ffe699;
}

.otherSite:hover {
  background-color: #5895b7;
}

.bg-theme {
    background-color: #457d04  !important;
}

.pl-2, .px-2 {
    padding-left: 0.5rem!important;
}
.pb-2, .py-2 {
    padding-bottom: 0.5rem!important;
}
.pr-2, .px-2 {
    padding-right: 0.5rem!important;
}
.pt-2, .py-2 {
    padding-top: 0.5rem!important;
}
.mr-2, .mx-2 {
    margin-right: 0.5rem!important;
}

div#photos_div{
	z-index:2000;
	position:absolute;
	top:13%;
	left:28%;
	width:48%;
	height:25vw;
	border:0px solid black;
	overflow: hidden;
	box-shadow:2px 2px 2px #aaaaaa; 
	background-color: #e2e2e2;
}

.tips {
    background-color: lightyellow;
    font-family: Calibri;
    margin-bottom: 8px;
	margin-top: 8px;
    text-align: center;
    padding-top: 3px;
    padding-bottom: 3px;
    width: 80%;
    text-decoration: none;
    font-size: 14px;
    color: blue;
    display: inline-block;
    letter-spacing: 0.04em;
}



@media (max-width: 1900px) {
	.menubar_4{
    opacity:1;
    font-family: Calibri;
    z-index: -1500;
    position:absolute;
	left: 83%;
	width:15%;
	top:93%;
  }
  
   div#desktop{
	position:absolute;
	top:103%;
	font-family:Calibri;
	font-size:16px;width:89%;
	text-align:center;
	background-color:#7fbf1b;
	height:80px;
	width:100%;
 }

   div#footer{
      position:absolute;
      width:100%;
      top:161%;  //initially 120%
      bottom:0;
      margin-bottom: 0;
      z-index:7000;
	  width:100%;
  }
  
}

@media (max-width: 1700px) {
	.menubar_4{
    opacity:1;
    font-family: Calibri;
    z-index: -1500;
    position:absolute;
	left: 83%;
	width:15%;
	top:102%;
  }
  
  div#tips_div{
            height:5cm;
            overflow: hidden;
            margin-bottom:5%;
			margin-top:1%;
            box-shadow:4px 4px 2px #aaaaaa;
            margin-left:5%;
            width:99%;
            border-radius: 0 0 5px 5px;
            background-color: #ffe699
        }
  
   div#desktop{
	position:absolute;
	top:103%;
	font-family:Calibri;
	font-size:16px;width:89%;
	text-align:center;
	background-color:#7fbf1b;
	height:80px;
	width:100%;
 }
 

   div#footer{
      position:absolute;
      width:100%;
      top:170%;  //initially 120%
      bottom:0;
      margin-bottom: 0;
      z-index:7000;
	  width:100%;
  }
  
  div#footer_photo{
      position:absolute;
      width:100%;
      top:138%;  //initially 120%
      bottom:0;
      margin-bottom: 0;
      z-index:7000;
	  width:100%;
  }
  
  div#photos_div{
	z-index:2000;
	position:absolute;
	top:16%;
	left:28%;
	width:48%;
	height:25vw;
	border:0px solid black;
	overflow: hidden;
	box-shadow:2px 2px 2px #aaaaaa; 
}

.news_circular_date {
    font-size: 13px;
    padding: 4px 8px;
    background-color: #c82333 !important;
    color: #fff !important;
    font-weight: 600;
    border-radius: 20px;
	width:25%;
}

.menubar_2{
	width: 100%;
    opacity:1;
    font-family: Calibri;
    z-index: -1500;
    position:absolute;
	width:45%;
	top:75%;
  }
  
}



