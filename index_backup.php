<?php include 'header.php'; ?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Frontier Basins</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: #fafafa; /* soft background *.carou.carousel-container {
  width: 90% !important;
  margin: 20px auto !important;
  clear: both;
  overflow: hidden;
  position: relative;
}iner {
  width: 90% !important;
  margin: 20px auto !important;
  clear: both;
  overflow: hidden;
  position: relative;
}

.content-area {
  flex: 1;
  padding: 20px;
}
  margin-left: auto;
  margin-right: 15%;  /* shift container to the right */
  clear: both;   /* ensures table doesn't float beside */
  overflow: hidden; 
  position: relative;
}    }
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
   overflow-y: auto;  /* always show vertical scrollbar if content is long */
  overflow-x: hidden; /* disable horizontal scroll (optional) */
}

    /* Header styling */


    /* Navbar styling */
    nav {
      background: #f1f3f4;  /* very light grey */
      border-bottom: 1px solid #dcdcdc;
    }

    nav ul {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: flex-start; /* left align */
	  margin-left: 300px; /*  margin from left */
    }

    nav ul li {
      position: relative;
    }

    nav ul li a {
      display: block;
      padding: 16px 23px;  /*  increased vertical padding for taller navbar */
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: background 0.2s;
    }

    nav ul li a:hover {
      background: #e6e6e6; /* soft hover */
    }

    /* Dropdown menu */
    nav ul li ul {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      background: #f8f9fa;
      min-width: 180px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.05);
      padding: 0;
      margin: 0;
    }

    nav ul li a {
      display: block;
      padding: 1rem 1.4rem; /* Use rem for scalability */
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: background 0.2s;
      white-space: nowrap;
      font-size: 0.95rem; /* Responsive font size */
    }

    nav ul li a:hover {
      background: #e6e6e6;
    }

    /* Dropdown menu */
    nav ul li ul {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      background: #f8f9fa;
      min-width: 180px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.05);
      padding: 0;
      margin: 0;
      z-index: 1000;
    }

    nav ul li ul li {
      width: 100%;
    }

    nav ul li:hover ul {
      display: block;
    }

    nav ul li ul li a {
      padding: 10px 15px;
      color: #444;
    }

    nav ul li ul li a:hover {
      background: #eaeaea;
    }

    main {
      padding: 20px;
    }
  </style>
</head>
<body>

 

  <nav>
    <button class="mobile-menu-btn" onclick="toggleMobileMenu()">☰</button>
    <ul id="navMenu">
      <li><a href="#">Home</a></li>
      <li><a href="#">About</a></li>
      <li>
        <a href="#">Support Services ▾</a>
        <ul>
		 
          <li><a target="_BLANK" href="https://fbddnlgts.ongc.co.in/travel/">FB Logistics Portal</a></li>
          <li><a href="#">Web Development</a></li>
          <li><a href="#">SEO</a></li>
        </ul>
      </li>
	  <li>
        <a href="#">Directory ▾</a>
        <ul>
          <li><a href="Manpower_list.pdf" target='_BLANK'>FB Directory</a></li>
          
        </ul>
      </li>
      
	  <li>
        <a href="#">Tools ▾</a>
        <ul>
		 
          <li><a target="_BLANK" href="https://pdftools.ongc.co.in">PDF Tools</a></li>
          
        </ul>
      </li>
      <li><a href="#">Contact</a></li>
    </ul>
  </nav>
  <div style="position:absolute; top:130px; left:20px; width:280px; text-align:center;">
  
  <!-- Head Manager -->
   <div style="position:relative; margin-bottom:20px;">
    <img src="BM.jpg" style="width:100%; height:auto;" alt="Head Manager" >
    <!-- Text inside image -->
    <!-- Text inside image -->
    <div style="
      position:absolute;
      bottom:8px;
      left:0;
      right:0;
      background:rgba(255,255,255,0.8);
      color:blue;
      padding:5px;
      font-weight:bold;
      border-bottom-left-radius:8px;
      border-bottom-right-radius:8px;
      white-space:nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
      font-size: 14px;
    ">
      Dr. R.S.Tandon–Basin Manager
    </div>
  </div>
  <!-- Other Managers Carousel -->
  </div>
  <div class="main-layout">
  
  
  <!-- Carousel -->
  <div id="carousel-main" class="carousel-container" style="width:90% !important; margin:20px auto !important;">
    <div class="carousel-slide fade">
      <img src="1.jpg" alt="Image 1" style="width:100%; height:auto;">
	   <div class="carousel-caption">Welcome to Our Company</div>
    </div>
    <div class="carousel-slide fade">
      <img src="2.jpg" alt="Image 2" style="width:100%; height:auto;">
	   <div class="carousel-caption">Our Manager Leads with Vision</div>
    </div>
  <div class="circulars-section">
     
      <?php include 'circulars_table.php'; ?>
    </div>
  </div>
  
  

  
<!-- Right Sidebar -->
<!-- Right Sidebar -->
<aside class="sidebar">
  <h3>Important Links</h3>
  <ul>
    <li><a href="#">DISHA</a></li>
    <li><a target="_BLANK" href="https://webice.ongc.co.in/irj/portal">ONGC WEBICE</a></li>
    <li><a target="_BLANK" href="https://mail.ongc.co.in/">ONGC WEBMAIL</a></li>
	 <li><a target="_BLANK" href="https://reports.ongc.co.in">ONGC REPORTS</a></li>
    <li><a href="#">ORGANOGRAM</a></li>
    <li><a href="#">PHOTO GALLERY</a></li>
	<li><a href="#">FEEDBACK</a></li>
  </ul>
</aside>
 </div>
<style>
  .sidebar {
  width: 300px;
  height:305px;
  background: #f4f6f7;
  border-left: 1px solid #ddd;
  padding: 20px;
  margin-top: 0px;    /* below navbar */
  position: absolute;  /* take it out of flex flow */
  top: 0;              /* relative to .main-layout */
  right: 20px;         /* stick to the right edge */
  overflow-y: auto; 
}
  .sidebar h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #444;
  }

  .sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .sidebar ul li {
    margin: 0;
    border-bottom: 1px solid #ddd; /* separator line */
  }

  .sidebar ul li:last-child {
    border-bottom: none; /* remove line after last item */
  }

  .sidebar ul li a {
    display: block;
    padding: 10px 0;
    text-decoration: none;
    color: #333;
    font-size: 16px;
    transition: color 0.2s;
  }

  .sidebar ul li a:hover {
    color: #0073e6;
  }

  /* Carousel Styles */
  .carousel-slide {
    display: none;
    position: relative;
  }

  .carousel-slide img {
    width: 100%;
    border-radius: 10px;
  }

  /* Caption text */
  .carousel-caption {
    position: absolute;
    bottom: 0.6rem;            /* Position at bottom of image */
    left: 45%;               /* Moved left from center */
    transform: translateX(-50%); /* Adjust centering for new left position */
    color: blue;             /* bold blue text */
    font-weight: bold;
    font-size: 1.25rem;
    background: rgba(255,255,255,0.8); /* slightly more opaque background */
    padding: 0.75rem 1rem;      /* increased padding for better visibility */
    border-radius: 0.5rem;      /* slightly more rounded */
    text-align: center;      /* center text within the box */
    white-space: nowrap;     /* prevent text wrapping */
    box-shadow: 0 2px 8px rgba(0,0,0,0.2); /* subtle shadow for depth */
  }

  .fade { 
    animation: fadeEffect 1.5s; 
  }

  @keyframes fadeEffect {
    from {opacity: 0.4;}
    to {opacity: 1;}
  }

  /* Circulars Section */
  .circulars-section {
    width: 100%;
    margin: 20px 0;
    clear: both;
  }

</style>

<style>
  /* Flexbox layout */
  .main-layout {
  display: block;      /* change from flex to block to allow normal flow */
  margin-top: 0;
  position: relative;
}

.carousel-container {
  width: 60%;
  margin: 0px auto;   /* ensures center alignment */
  
  clear: both;   /* ensures table doesn’t float beside */
  overflow: hidden; 
position: relative;
   
}
.content-area {
  flex: 1;
  padding: 20px;
}





.carousel-slide {
  position: relative;
}

.carousel-slide img {
  
 width: 100%;
  border-radius: 10px;
  
}

/* Caption text */
.carousel-caption {
  position: absolute;
  bottom: 10px;            /* Moved down - closer to bottom */
  left: 45%;               /* Moved left from center */
  transform: translateX(-50%); /* Adjust centering for new left position */
  color: blue;             /* bold blue text */
  font-weight: bold;
  font-size: 20px;
  background: rgba(255,255,255,0.8); /* slightly more opaque background */
  padding: 12px 16px;      /* increased padding for better visibility */
  border-radius: 8px;      /* slightly more rounded */
  text-align: center;      /* center text within the box */
  white-space: nowrap;     /* prevent text wrapping */
  box-shadow: 0 2px 8px rgba(0,0,0,0.2); /* subtle shadow for depth */
}

  .carousel-slide {
    display: none;
  }

  .carousel-container img {
  width: 100%;
  height: 100%;            /* forces all images to same height */
  object-fit: cover;       /* crops images without stretching */
  
  }

  .fade { animation: fadeEffect 1.5s; }

  @keyframes fadeEffect {
    from {opacity: 0.4;}
    to {opacity: 1;}
  }

  /* RESPONSIVE DESIGN - COMPREHENSIVE MEDIA QUERIES */
  
/* Large Desktop (1400px and below) */
@media (max-width: 87.5rem) { /* 1400px */
  nav ul {
    margin-left: min(15.6rem, 18vw); /* 250px with viewport fallback */
  }
  .head-manager {
    width: 16.25rem; /* 260px */
  }
  .sidebar {
    width: 17.5rem; /* 280px */
  }
  .carousel-container {
    width: 90%;          /* align with circulars section */
    margin: 20px auto;   /* same as circulars section */
  }
}

/* Desktop (1200px and below) */
@media (max-width: 75rem) { /* 1200px */
  nav ul {
    margin-left: min(12.5rem, 15vw); /* 200px with viewport fallback */
    gap: 0.5rem;
  }
  .head-manager {
    width: 13.75rem; /* 220px */
    top: 7.5rem; /* 120px */
  }
  .sidebar {
    width: 16.25rem; /* 260px */
    right: 0.6rem;
  }
  .carousel-container {
    width: 90%;          /* align with circulars section */
    margin: 20px auto;   /* same as circulars section */
  }
  .carousel-caption {
    font-size: 1.1rem;
    padding: 0.6rem 0.8rem;
  }
}

/* Tablet Landscape (992px and below) */
@media (max-width: 62rem) { /* 992px */
  nav ul {
    margin-left: min(9.4rem, 12vw);
    padding: 0 0.6rem;
  }
  nav ul li a {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .head-manager {
    width: 11.25rem; /* 180px */
    top: 6.9rem; /* 110px */
    left: 0.6rem;
  }
  
  .main-layout {
    flex-direction: column;
    align-items: center;
    padding: 0 0.5rem;
  }
  
  .carousel-container {
    width: 90%;          /* align with circulars section */
    margin: 1.25rem auto; /* maintain spacing on tablet */
  }
  
  .sidebar {
    position: relative;
    width: 90%;
    max-width: 37.5rem; /* 600px */
    height: auto;
    right: 0;
    top: 1.25rem;
    margin: 1.25rem auto;
  }
  
  .circulars-section {
    width: 95%;
    margin: 1.25rem auto;
  }
  
  .carousel-caption {
    font-size: 1rem;
    padding: 0.6rem 0.75rem;
    bottom: 0.5rem;
    left: 45%;
  }
}

/* Tablet Portrait (768px and below) */
@media (max-width: 48rem) { /* 768px */
  .mobile-menu-btn {
    display: block;
  }
  
  nav ul {
    margin-left: 1.25rem;
    flex-direction: column;
    background: #f1f3f4;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: none;
    z-index: 1000;
  }
  
  nav ul.show {
    display: flex;
  }
  
  nav ul li {
    width: 100%;
  }
  
  nav ul li a {
    padding: 0.9rem 1.25rem;
    border-bottom: 1px solid #ddd;
    font-size: 0.95rem;
  }
  
  .head-manager {
    position: relative;
    width: 90%;
    max-width: 18.75rem; /* 300px */
    top: 0;
    left: 0;
    margin: 1.25rem auto;
  }
  
  .carousel-container {
    width: 95%;
  }
  
  .carousel-caption {
    font-size: 1rem;
    padding: 0.5rem 0.6rem;
    bottom: 0.6rem;
  }
  
  .sidebar {
    width: 95%;
    padding: 0.9rem;
    margin: 1.25rem auto;
  }
  
  .sidebar h3 {
    font-size: 1.1rem;
  }
  
  .sidebar ul li a {
    font-size: 0.9rem;
    padding: 0.5rem 0;
  }
}
    left: 10px;
  }
  
  .main-layout {
    flex-direction: column;
    align-items: center;
  }
  
  .carousel-container {
    width: 80%;
    margin: 20px auto;
  }
  
  .sidebar {
    position: relative;
    width: 90%;
    max-width: 600px;
    height: auto;
    right: 0;
    top: 20px;
    margin: 20px auto;
  }
  
  .circulars-section {
    width: 95%;
    margin: 20px auto;
  }
}

/* Tablet Portrait */
@media (max-width: 768px) {
  nav ul {
    margin-left: 20px;
    flex-direction: column;
    background: #f1f3f4;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: none;
  }
  
  nav ul.show {
    display: flex;
  }
  
  nav ul li {
    width: 100%;
  }
  
  nav ul li a {
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
  }
  
  /* Mobile Menu Button */
  .mobile-menu-btn {
    display: block;
    background: none;
    border: none;
    font-size: 24px;
    padding: 10px;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
  }
  
  .head-manager {
    position: relative;
    width: 90%;
    max-width: 300px;
    top: 0;
}

/* Mobile (576px and below) */
@media (max-width: 36rem) { /* 576px */
  nav ul li a {
    padding: 0.75rem 0.9rem;
    font-size: 0.9rem;
  }
  
  .head-manager {
    width: 95%;
    margin: 0.9rem auto;
  }
  
  .head-manager img {
    max-width: 15.6rem; /* 250px */
  }
  
  .carousel-container {
    width: 98%;
    margin: 0.9rem auto;
  }
  
  .carousel-caption {
    font-size: 0.9rem;
    padding: 0.5rem 0.6rem;
    bottom: 0.6rem;
    max-width: 90%;
    white-space: normal;
  }
  
  .sidebar {
    width: 98%;
    padding: 0.6rem;
    height: auto;
  }
  
  .sidebar h3 {
    font-size: 1rem;
    margin-bottom: 0.6rem;
  }
  
  .sidebar ul li a {
    font-size: 0.85rem;
    padding: 0.4rem 0;
  }
  
  .circulars-section {
    width: 98%;
    margin: 0.9rem auto;
  }
}

/* Extra Small Mobile (480px and below) */
@media (max-width: 30rem) { /* 480px */
  .head-manager img {
    max-width: 12.5rem; /* 200px */
  }
  
  .carousel-caption {
    font-size: 0.75rem;
    padding: 0.4rem 0.5rem;
    bottom: 0.5rem;
    max-width: 85%;
  }
  
  .sidebar ul li a {
    font-size: 0.8rem;
    padding: 0.3rem 0;
  }
  
  .circulars-section h2 {
    font-size: 1.1rem;
    padding: 0.6rem 0.9rem;
  }
}

/* Ultra Small Mobile (360px and below) */
@media (max-width: 22.5rem) { /* 360px */
  nav ul li a {
    padding: 0.6rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .head-manager {
    width: 100%;
    margin: 0.5rem auto;
  }
  
  .head-manager img {
    max-width: 10rem; /* 160px */
  }
  
  .carousel-caption {
    font-size: 0.7rem;
    padding: 0.3rem 0.4rem;
    bottom: 0.3rem;
  }
  
  .sidebar {
    padding: 0.5rem;
  }
  
  .sidebar h3 {
    font-size: 0.9rem;
  }
  
  .sidebar ul li a {
    font-size: 0.75rem;
    padding: 0.25rem 0;
  }
}

/* Utility Classes for Enhanced Responsiveness */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Print Styles */
@media print {
  .mobile-menu-btn,
  nav ul li ul,
  .sidebar {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .carousel-container {
    width: 100%;
  }
}

</style>
    text-align: center;
  }
}

/* Hide mobile menu button by default */
.mobile-menu-btn {
  display: none;
}


</style>


<script>
  let slideIndex = 0;
  showSlides();

  function showSlides() {
    let slides = document.getElementsByClassName("carousel-slide");
    for (let i = 0; i < slides.length; i++) {
      slides[i].style.display = "none";  
    }
    slideIndex++;
    if (slideIndex > slides.length) {slideIndex = 1}    
    slides[slideIndex-1].style.display = "block";  
    setTimeout(showSlides, 3000); // Change image every 3 seconds
  }

  // Mobile menu toggle function
  function toggleMobileMenu() {
    const navMenu = document.getElementById('navMenu');
    navMenu.classList.toggle('show');
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const nav = document.querySelector('nav');
    const navMenu = document.getElementById('navMenu');
    const mobileBtn = document.querySelector('.mobile-menu-btn');
    
    if (!nav.contains(event.target) && navMenu.classList.contains('show')) {
      navMenu.classList.remove('show');
    }
  });

  // Handle window resize
  window.addEventListener('resize', function() {
    const navMenu = document.getElementById('navMenu');
    if (window.innerWidth > 768) {
      navMenu.classList.remove('show');
    }
  });
</script>

<!-- Force carousel alignment with Orders & Circulars -->
<style>
.main-layout .carousel-container {
  width: 90% !important;
  margin: 20px auto !important;
  max-width: none !important;
  margin-left: auto !important;
  margin-right: auto !important;
  display: block !important;
}

/* Also target any nested containers */
div.carousel-container {
  width: 90% !important;
  margin: 20px auto !important;
}
</style>

  <!-- Content -->
  <!--<div class="container my-4">
    <h2>Welcome</h2>
    <p>This is the main content area below the header and navigation bar.</p>
  </div>-->

  <!-- Bootstrap JS -->
  <script src="bootstrap.bundle.min.js"></script>
</body>

</html>
